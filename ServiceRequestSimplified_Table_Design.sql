-- ServiceRequestSimplified Table
-- This table is derived from the ServiceRequest table with the following fields removed:
-- Payment information: InviteToPayId, Lmr<PERSON><PERSON>, PurchaseOrder, TrapAuth
-- Problem reference: Problems collection, OtherProblem
-- Status: ServiceRequestStatusId, ServiceRequestStatus
-- Account: AccountAuthorizationNumber
-- Group: ServiceRequestGroupId, ServiceRequestGroup
-- Address reference: DropShipAddressId, ShipToMetadata, ShipToSiteNumber
-- Flags: IsRealTime, IsDeniedWarranty

CREATE TABLE ServiceRequestSimplified (
    -- Core Identity Fields
    Id INT IDENTITY(1,1) NOT NULL,
    ExternalId UNIQUEIDENTIFIER NOT NULL,
    ServiceRequestNumber NVARCHAR(255) NULL,
    
    -- Product Information
    Sku NVARCHAR(255) NOT NULL,
    SerialNumber NVARCHAR(35) NULL,
    IsSerialNumberUnreadable BIT NOT NULL,
    
    -- Purchase Information
    IsProofOfPurchaseIncluded BIT NOT NULL,
    PurchaseDate DATE NULL,
    
    -- Tool Information
    ToolNickname NVARCHAR(100) NULL,
    
    -- Service Information
    OtherInclusion NVARCHAR(500) NULL,
    RepairLocationId INT NULL,
    DeliveryTypeId INT NOT NULL,
    RepairDecisionId INT NULL,
    RepairDecisionBy NVARCHAR(255) NULL,
    
    -- Tracking Information
    SubmittedDate DATETIMEOFFSET NULL,
    ShelfLocation NVARCHAR(50) NULL,
    MasterTrackingNumber NVARCHAR(50) NULL,
    OutboundTrackingNumber NVARCHAR(500) NULL,
    OracleTrapReference NVARCHAR(255) NULL,
    
    -- Delivery Information
    -- Note: Removed DropShipAddressId, ShipToMetadata, ShipToSiteNumber
    
    -- Flags and Status
    ProblemIsNotProvided BIT NOT NULL,
    -- Note: Removed IsRealTime, IsDeniedWarranty
    
    -- Generic Account Flag (NotMapped in original)
    -- IsGenericAccount - This was NotMapped, so not included in table
    
    -- Audit Fields
    CreatedBy NVARCHAR(255) NOT NULL,
    CreatedAt DATETIMEOFFSET NOT NULL,
    UpdatedBy NVARCHAR(255) NOT NULL,
    UpdatedAt DATETIMEOFFSET NOT NULL,
    
    -- Row Version for Concurrency
    RowVersion ROWVERSION,
    
    -- Constraints
    CONSTRAINT PK_ServiceRequestSimplified PRIMARY KEY (Id),
    CONSTRAINT UIDX_ServiceRequestSimplified_ExternalId UNIQUE (ExternalId),
    CONSTRAINT FK_ServiceRequestSimplified_RepairLocationId_Location FOREIGN KEY (RepairLocationId) REFERENCES Location (Id),
    CONSTRAINT FK_ServiceRequestSimplified_DeliveryTypeId_DeliveryType FOREIGN KEY (DeliveryTypeId) REFERENCES DeliveryType (Id),
    CONSTRAINT FK_ServiceRequestSimplified_RepairDecisionId_RepairDecision FOREIGN KEY (RepairDecisionId) REFERENCES RepairDecision (Id)
);

-- Indexes for performance
CREATE INDEX IDX_ServiceRequestSimplified_ServiceRequestNumber ON ServiceRequestSimplified(ServiceRequestNumber);
CREATE INDEX IDX_ServiceRequestSimplified_Sku ON ServiceRequestSimplified(Sku);
CREATE INDEX IDX_ServiceRequestSimplified_RepairLocationId ON ServiceRequestSimplified(RepairLocationId);
CREATE INDEX IDX_ServiceRequestSimplified_CreatedAt ON ServiceRequestSimplified(CreatedAt);

-- Related tables that would still be needed (simplified versions):

-- ServiceRequestSimplifiedInclusion (instead of ServiceRequestInclusion)
CREATE TABLE ServiceRequestSimplifiedInclusion (
    ServiceRequestSimplifiedId INT NOT NULL,
    InclusionId INT NOT NULL,
    
    CONSTRAINT PK_ServiceRequestSimplifiedInclusion PRIMARY KEY (ServiceRequestSimplifiedId, InclusionId),
    CONSTRAINT FK_ServiceRequestSimplifiedInclusion_ServiceRequestSimplifiedId FOREIGN KEY (ServiceRequestSimplifiedId) REFERENCES ServiceRequestSimplified (Id),
    CONSTRAINT FK_ServiceRequestSimplifiedInclusion_InclusionId FOREIGN KEY (InclusionId) REFERENCES Inclusion (Id)
);

-- ServiceRequestSimplifiedAttachment (instead of ServiceRequestAttachment)
CREATE TABLE ServiceRequestSimplifiedAttachment (
    ServiceRequestSimplifiedId INT NOT NULL,
    AttachmentId INT NOT NULL,
    CreatedAt DATETIMEOFFSET NOT NULL,
    CreatedBy NVARCHAR(255) NOT NULL,
    UpdatedAt DATETIMEOFFSET NOT NULL,
    UpdatedBy NVARCHAR(255) NOT NULL,
    
    CONSTRAINT PK_ServiceRequestSimplifiedAttachment PRIMARY KEY (ServiceRequestSimplifiedId, AttachmentId),
    CONSTRAINT FK_ServiceRequestSimplifiedAttachment_ServiceRequestSimplifiedId FOREIGN KEY (ServiceRequestSimplifiedId) REFERENCES ServiceRequestSimplified (Id),
    CONSTRAINT FK_ServiceRequestSimplifiedAttachment_AttachmentId FOREIGN KEY (AttachmentId) REFERENCES Attachment (Id)
);

-- ServiceRequestSimplifiedEvent (instead of ServiceRequestEvent)
CREATE TABLE ServiceRequestSimplifiedEvent (
    Id INT IDENTITY(1,1) NOT NULL,
    ServiceRequestSimplifiedId INT NOT NULL,
    EventTypeId INT NOT NULL,
    Note NVARCHAR(MAX) NULL,
    CreatedBy NVARCHAR(255) NOT NULL,
    CreatedAt DATETIMEOFFSET NOT NULL,
    
    CONSTRAINT PK_ServiceRequestSimplifiedEvent PRIMARY KEY (Id),
    CONSTRAINT FK_ServiceRequestSimplifiedEvent_ServiceRequestSimplifiedId FOREIGN KEY (ServiceRequestSimplifiedId) REFERENCES ServiceRequestSimplified (Id),
    CONSTRAINT FK_ServiceRequestSimplifiedEvent_EventTypeId FOREIGN KEY (EventTypeId) REFERENCES EventType (Id)
);
