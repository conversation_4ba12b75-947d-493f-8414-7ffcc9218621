-- UnclaimedOrder Table
-- This table is derived from the ServiceRequest table with the following fields removed:
-- Payment information: InviteToPayId, LmrPrice, PurchaseOrder, TrapAuth
-- Problem reference: Problems collection, OtherProblem
-- Status: ServiceRequestStatusId, ServiceRequestStatus
-- Account: AccountAuthorizationNumber
-- Group: ServiceRequestGroupId, ServiceRequestGroup
-- Address reference: DropShipAddressId, ShipToMetadata, ShipToSiteNumber
-- Flags: IsRealTime, IsDeniedWarranty

CREATE TABLE UnclaimedOrder (
    -- Core Identity Fields
    Id INT IDENTITY(1,1) NOT NULL,
    ExternalId UNIQUEIDENTIFIER NOT NULL,
    ServiceRequestNumber NVARCHAR(255) NULL,
    
    -- Product Information
    Sku NVARCHAR(255) NOT NULL,
    ServiceRequestId INT NULL,
    IsSerialNumberUnreadable BIT NOT NULL,
    
    -- Tool Information
    ToolNickname NVARCHAR(100) NULL,
    
    -- Service Information
    OtherInclusion NVARCHAR(500) NULL,
    RepairLocationId INT NULL,

    -- Tracking Information
    MasterTrackingNumber NVARCHAR(50) NULL,
    OutboundTrackingNumber NVARCHAR(500) NULL,
    
    -- Delivery Information
    -- Note: Removed DropShipAddressId, ShipToMetadata, ShipToSiteNumber
    
    -- Flags and Status
    ProblemIsNotProvided BIT NOT NULL,
    -- Note: Removed IsRealTime, IsDeniedWarranty
    
    -- Generic Account Flag (NotMapped in original)
    -- IsGenericAccount - This was NotMapped, so not included in table
    
    -- Audit Fields
    CreatedBy NVARCHAR(255) NOT NULL,
    CreatedAt DATETIMEOFFSET NOT NULL,
    UpdatedBy NVARCHAR(255) NOT NULL,
    UpdatedAt DATETIMEOFFSET NOT NULL,
    
    -- Row Version for Concurrency
    RowVersion ROWVERSION,
    
    -- Constraints
    CONSTRAINT PK_UnclaimedOrder PRIMARY KEY (Id),
    CONSTRAINT UIDX_UnclaimedOrder_ExternalId UNIQUE (ExternalId),
    CONSTRAINT FK_UnclaimedOrder_RepairLocationId_Location FOREIGN KEY (RepairLocationId) REFERENCES Location (Id),
    CONSTRAINT FK_UnclaimedOrder_ServiceRequestId_ServiceRequest FOREIGN KEY (ServiceRequestId) REFERENCES ServiceRequest (Id)
);

-- Indexes for performance
CREATE INDEX IDX_UnclaimedOrder_ServiceRequestNumber ON UnclaimedOrder(ServiceRequestNumber);
CREATE INDEX IDX_UnclaimedOrder_Sku ON UnclaimedOrder(Sku);
CREATE INDEX IDX_UnclaimedOrder_RepairLocationId ON UnclaimedOrder(RepairLocationId);
CREATE INDEX IDX_UnclaimedOrder_CreatedAt ON UnclaimedOrder(CreatedAt);

-- Related tables that would still be needed:

-- UnclaimedOrderInclusion (instead of ServiceRequestInclusion)
CREATE TABLE UnclaimedOrderInclusion (
    UnclaimedOrderId INT NOT NULL,
    InclusionId INT NOT NULL,

    CONSTRAINT PK_UnclaimedOrderInclusion PRIMARY KEY (UnclaimedOrderId, InclusionId),
    CONSTRAINT FK_UnclaimedOrderInclusion_UnclaimedOrderId FOREIGN KEY (UnclaimedOrderId) REFERENCES UnclaimedOrder (Id),
    CONSTRAINT FK_UnclaimedOrderInclusion_InclusionId FOREIGN KEY (InclusionId) REFERENCES Inclusion (Id)
);
