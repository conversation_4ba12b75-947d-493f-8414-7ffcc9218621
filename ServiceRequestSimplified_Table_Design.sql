CREATE TABLE UnclaimedOrder (
    Id INT IDENTITY(1,1) NOT NULL,
    ExternalId UNIQUEIDENTIFIER NOT NULL,
    Sku NVARCHAR(255) NOT NULL,
    ServiceRequestId INT NULL,
    IsSerialNumberUnreadable BIT NOT NULL,
    ToolNickname NVARCHAR(100) NULL,
    OtherInclusion NVARCHAR(500) NULL,
    RepairLocationId INT NULL,
    MasterTrackingNumber NVARCHAR(50) NULL,
    OutboundTrackingNumber NVARCHAR(500) NULL,
    ProblemIsNotProvided BIT NOT NULL,
    CreatedBy NVARCHAR(255) NOT NULL,
    CreatedAt DATETIMEOFFSET NOT NULL,
    UpdatedBy NVARCHAR(255) NOT NULL,
    UpdatedAt DATETIMEOFFSET NOT NULL,
    RowVersion ROWVERSION,

    CONSTRAINT PK_UnclaimedOrder PRIMARY KEY (Id),
    CONSTRAINT UIDX_UnclaimedOrder_ExternalId UNIQUE (ExternalId),
    CONSTRAINT FK_UnclaimedOrder_RepairLocationId_Location FOREIGN KEY (RepairLocationId) REFERENCES Location (Id),
    CONSTRAINT FK_UnclaimedOrder_ServiceRequestId_ServiceRequest FOREIGN KEY (ServiceRequestId) REFERENCES ServiceRequest (Id)
);

CREATE INDEX IDX_UnclaimedOrder_Sku ON UnclaimedOrder(Sku);
CREATE INDEX IDX_UnclaimedOrder_RepairLocationId ON UnclaimedOrder(RepairLocationId);
CREATE INDEX IDX_UnclaimedOrder_CreatedAt ON UnclaimedOrder(CreatedAt);

CREATE TABLE UnclaimedOrderInclusion (
    UnclaimedOrderId INT NOT NULL,
    InclusionId INT NOT NULL,
    CONSTRAINT PK_UnclaimedOrderInclusion PRIMARY KEY (UnclaimedOrderId, InclusionId),
    CONSTRAINT FK_UnclaimedOrderInclusion_UnclaimedOrderId FOREIGN KEY (UnclaimedOrderId) REFERENCES UnclaimedOrder (Id),
    CONSTRAINT FK_UnclaimedOrderInclusion_InclusionId FOREIGN KEY (InclusionId) REFERENCES Inclusion (Id)
);
