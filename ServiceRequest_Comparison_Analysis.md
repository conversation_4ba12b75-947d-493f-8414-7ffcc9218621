# ServiceRequest Table Analysis and Simplification

## Overview
This document shows the analysis of the current ServiceRequest table and the creation of a simplified version with specific fields removed as requested.

## Fields Removed from Original ServiceRequest Table

### 1. Payment Information (Removed)
| Field Name | Data Type | Description |
|------------|-----------|-------------|
| `InviteToPayId` | NVARCHAR(50) | Payment invitation identifier |
| `LmrPrice` | DECIMAL(10,2) | LMR pricing information |
| `PurchaseOrder` | NVARCHAR(20) | Purchase order number |
| `TrapAuth` | NVARCHAR(100) | Payment authorization |

### 2. Problem Reference (Removed)
| Field Name | Data Type | Description |
|------------|-----------|-------------|
| `OtherProblem` | NVARCHAR(500) | Other problem description |
| `Problems` | Collection | ServiceRequestProblem relationship |

### 3. Status (Removed)
| Field Name | Data Type | Description |
|------------|-----------|-------------|
| `ServiceRequestStatusId` | INT | Status identifier |
| `ServiceRequestStatus` | Navigation | Status entity relationship |

### 4. Account (Removed)
| Field Name | Data Type | Description |
|------------|-----------|-------------|
| `AccountAuthorizationNumber` | NVARCHAR(100) | Account authorization |

### 5. Group (Removed)
| Field Name | Data Type | Description |
|------------|-----------|-------------|
| `ServiceRequestGroupId` | INT | Group identifier |
| `ServiceRequestGroup` | Navigation | Group entity relationship |

### 6. Address Reference (Removed)
| Field Name | Data Type | Description |
|------------|-----------|-------------|
| `DropShipAddressId` | NVARCHAR(50) | Drop ship address reference |
| `ShipToMetadata` | NVARCHAR(MAX) | Shipping metadata JSON |
| `ShipToSiteNumber` | NVARCHAR(50) | Ship to site number |

### 7. Flags (Removed)
| Field Name | Data Type | Description |
|------------|-----------|-------------|
| `IsRealTime` | BIT | Real-time processing flag |
| `IsDeniedWarranty` | BIT | Denied warranty flag |

## Fields Retained in ServiceRequestSimplified Table

### Core Identity
- `Id` (INT IDENTITY) - Primary key
- `ExternalId` (UNIQUEIDENTIFIER) - External system identifier
- `ServiceRequestNumber` (NVARCHAR(255)) - Service request number

### Product Information
- `Sku` (NVARCHAR(255)) - Product SKU
- `SerialNumber` (NVARCHAR(35)) - Product serial number
- `IsSerialNumberUnreadable` (BIT) - Serial number readability flag

### Purchase Information
- `IsProofOfPurchaseIncluded` (BIT) - Proof of purchase flag
- `PurchaseDate` (DATE) - Purchase date

### Tool Information
- `ToolNickname` (NVARCHAR(100)) - Tool nickname

### Service Information
- `OtherInclusion` (NVARCHAR(500)) - Other inclusions description
- `RepairLocationId` (INT) - Repair location reference
- `DeliveryTypeId` (INT) - Delivery type reference
- `RepairDecisionId` (INT) - Repair decision reference
- `RepairDecisionBy` (NVARCHAR(255)) - Who made repair decision

### Tracking Information
- `SubmittedDate` (DATETIMEOFFSET) - Submission date
- `ShelfLocation` (NVARCHAR(50)) - Shelf location
- `MasterTrackingNumber` (NVARCHAR(50)) - Master tracking number
- `OutboundTrackingNumber` (NVARCHAR(500)) - Outbound tracking
- `OracleTrapReference` (NVARCHAR(255)) - Oracle TRAP reference

### Flags and Status
- `ProblemIsNotProvided` (BIT) - Problem not provided flag

### Audit Fields
- `CreatedBy` (NVARCHAR(255)) - Created by user
- `CreatedAt` (DATETIMEOFFSET) - Creation timestamp
- `UpdatedBy` (NVARCHAR(255)) - Updated by user
- `UpdatedAt` (DATETIMEOFFSET) - Update timestamp
- `RowVersion` (ROWVERSION) - Concurrency control

## Related Tables Also Simplified

### ServiceRequestSimplifiedInclusion
- Replaces `ServiceRequestInclusion`
- Links to inclusions without group/status dependencies

### ServiceRequestSimplifiedAttachment
- Replaces `ServiceRequestAttachment`
- Maintains attachment relationships

### ServiceRequestSimplifiedEvent
- Replaces `ServiceRequestEvent`
- Maintains event history

## Summary of Changes

**Total Original Fields**: ~45+ fields (including navigation properties)
**Total Simplified Fields**: 22 fields
**Fields Removed**: ~23+ fields
**Percentage Reduction**: ~51% reduction in complexity

The simplified table maintains core service request functionality while removing:
- Payment processing complexity
- Problem categorization
- Status management
- Account management
- Group relationships
- Complex address handling
- Real-time and warranty denial flags

This creates a more focused table for basic service request tracking without the overhead of payment, status, and complex relationship management.
