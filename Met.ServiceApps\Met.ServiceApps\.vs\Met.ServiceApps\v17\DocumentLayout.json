{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{7AD99D74-52C9-4303-ACD0-069D72EC5043}|src\\SharedServices\\Orders\\src\\Orders.Api\\Orders.Api.csproj|c:\\users\\<USER>\\source\\repos\\met.serviceapps\\met.serviceapps\\src\\sharedservices\\orders\\src\\orders.api\\application\\salesorders\\requesthandlers\\v2\\createsalesorderpaymentrequesthandlerv2.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7AD99D74-52C9-4303-ACD0-069D72EC5043}|src\\SharedServices\\Orders\\src\\Orders.Api\\Orders.Api.csproj|solutionrelative:src\\sharedservices\\orders\\src\\orders.api\\application\\salesorders\\requesthandlers\\v2\\createsalesorderpaymentrequesthandlerv2.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FC84047C-6FBF-4464-BE52-5CA52D506381}|src\\SharedServices\\RestApi\\src\\SharedServices.Rest.Api\\SharedServices.Rest.Api.csproj|c:\\users\\<USER>\\source\\repos\\met.serviceapps\\met.serviceapps\\src\\sharedservices\\restapi\\src\\sharedservices.rest.api\\application\\payment\\requesthandlers\\salesorderinvitetopayprocessedrequesthandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FC84047C-6FBF-4464-BE52-5CA52D506381}|src\\SharedServices\\RestApi\\src\\SharedServices.Rest.Api\\SharedServices.Rest.Api.csproj|solutionrelative:src\\sharedservices\\restapi\\src\\sharedservices.rest.api\\application\\payment\\requesthandlers\\salesorderinvitetopayprocessedrequesthandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "CreateSalesOrderPaymentRequestHandlerV2.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\SharedServices\\Orders\\src\\Orders.Api\\Application\\SalesOrders\\RequestHandlers\\V2\\CreateSalesOrderPaymentRequestHandlerV2.cs", "RelativeDocumentMoniker": "src\\SharedServices\\Orders\\src\\Orders.Api\\Application\\SalesOrders\\RequestHandlers\\V2\\CreateSalesOrderPaymentRequestHandlerV2.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\SharedServices\\Orders\\src\\Orders.Api\\Application\\SalesOrders\\RequestHandlers\\V2\\CreateSalesOrderPaymentRequestHandlerV2.cs", "RelativeToolTip": "src\\SharedServices\\Orders\\src\\Orders.Api\\Application\\SalesOrders\\RequestHandlers\\V2\\CreateSalesOrderPaymentRequestHandlerV2.cs", "ViewState": "AgIAACYAAAAAAAAAAAAowCwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-18T20:27:59.338Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "SalesOrderInviteToPayProcessedRequestHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\SharedServices\\RestApi\\src\\SharedServices.Rest.Api\\Application\\Payment\\RequestHandlers\\SalesOrderInviteToPayProcessedRequestHandler.cs", "RelativeDocumentMoniker": "src\\SharedServices\\RestApi\\src\\SharedServices.Rest.Api\\Application\\Payment\\RequestHandlers\\SalesOrderInviteToPayProcessedRequestHandler.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\SharedServices\\RestApi\\src\\SharedServices.Rest.Api\\Application\\Payment\\RequestHandlers\\SalesOrderInviteToPayProcessedRequestHandler.cs", "RelativeToolTip": "src\\SharedServices\\RestApi\\src\\SharedServices.Rest.Api\\Application\\Payment\\RequestHandlers\\SalesOrderInviteToPayProcessedRequestHandler.cs", "ViewState": "AgIAAAcAAAAAAAAAAAAwwBQAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-18T20:27:47.511Z"}]}]}]}